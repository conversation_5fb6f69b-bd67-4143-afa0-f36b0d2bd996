import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BulkVehicleReservationIdsResponseDto {
  @ApiProperty({
    description: 'Array of created vehicle reservation IDs',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-426614174000',
      '123e4567-e89b-12d3-a456-426614174001',
    ],
  })
  ids: string[];

  @ApiProperty({
    description: 'Number of vehicle reservations successfully created',
    example: 2,
  })
  created: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully created 2 vehicle reservations',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Array of failed vehicle reservations with error details',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        index: { type: 'number', description: 'Index of failed reservation' },
        reservationNumber: { type: 'string', description: 'Reservation number that failed' },
        error: { type: 'string', description: 'Error message' },
      },
    },
  })
  failed?: Array<{
    index: number;
    reservationNumber: string;
    error: string;
  }>;
}

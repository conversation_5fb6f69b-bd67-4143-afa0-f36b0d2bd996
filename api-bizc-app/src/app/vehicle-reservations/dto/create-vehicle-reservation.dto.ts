import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsEnum,
  IsDateString,
  IsDecimal,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsNumber,
  Min,
  Max,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  VehicleReservationStatus,
  VehiclePickupLocationType,
  PaymentStatus,
  TaxType,
  DiscountType,
} from '../../shared/types/vehicle.enum';
import {
  ReservationSource,
} from '../../shared/types/accommodation.enum';

export class CreateVehicleReservationVehicleDto {
  @ApiProperty({ description: 'Vehicle ID' })
  @IsNotEmpty()
  @IsUUID()
  vehicleId: string;

  @ApiProperty({
    description: 'Daily rate for this vehicle',
    example: '150.00',
  })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  vehicleDailyRate: string;

  @ApiPropertyOptional({
    enum: DiscountType,
    description: 'Vehicle-specific discount type',
  })
  @IsOptional()
  @IsEnum(DiscountType)
  vehicleDiscountType?: DiscountType;

  @ApiPropertyOptional({
    description: 'Vehicle-specific discount value',
    example: '10.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  vehicleDiscountValue?: string;

  @ApiPropertyOptional({
    description: 'Vehicle-specific discount reason',
  })
  @IsOptional()
  @IsString()
  vehicleDiscountReason?: string;

  @ApiPropertyOptional({
    description: 'Whether vehicle comes with driver',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  vehicleWithDriver?: boolean;

  @ApiPropertyOptional({
    description: 'Driver staff member ID',
  })
  @IsOptional()
  @IsUUID()
  vehicleDriverId?: string;

  @ApiPropertyOptional({
    description: 'Driver notes for this vehicle',
  })
  @IsOptional()
  @IsString()
  vehicleDriverNotes?: string;

  @ApiPropertyOptional({
    description: 'External driver name',
  })
  @IsOptional()
  @IsString()
  vehicleExternalDriverName?: string;

  @ApiPropertyOptional({
    description: 'External driver license number',
  })
  @IsOptional()
  @IsString()
  vehicleExternalDriverLicense?: string;

  @ApiPropertyOptional({
    description: 'External driver contact information',
  })
  @IsOptional()
  @IsString()
  vehicleExternalDriverContact?: string;

  @ApiPropertyOptional({
    description: 'Vehicle-specific pickup time',
  })
  @IsOptional()
  @IsDateString()
  vehiclePickUpTime?: string;

  @ApiPropertyOptional({
    description: 'Vehicle-specific return time',
  })
  @IsOptional()
  @IsDateString()
  vehicleReturnTime?: string;

  @ApiPropertyOptional({
    description: 'Vehicle-specific notes',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Vehicle order in multi-vehicle reservation',
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  vehicleOrder?: number;

  @ApiPropertyOptional({
    description: 'Tax rate ID for this vehicle',
  })
  @IsOptional()
  @IsUUID()
  vehicleTaxRateId?: string;
}

export class CreateVehicleReservationDto {
  @ApiProperty({
    description: 'Reservation number',
    maxLength: 191,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  reservationNumber: string;

  @ApiPropertyOptional({
    description: 'External booking reference number',
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  referenceNumber?: string;

  @ApiPropertyOptional({
    description: 'Customer ID',
  })
  @IsOptional()
  @IsUUID()
  customerId?: string;

  @ApiProperty({
    description: 'Pick up date and time',
  })
  @IsNotEmpty()
  @IsDateString()
  pickUpDate: string;

  @ApiProperty({
    description: 'Return date and time',
  })
  @IsNotEmpty()
  @IsDateString()
  returnDate: string;

  @ApiPropertyOptional({
    enum: VehicleReservationStatus,
    description: 'Reservation status',
    default: VehicleReservationStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(VehicleReservationStatus)
  status?: VehicleReservationStatus;

  @ApiPropertyOptional({
    enum: ReservationSource,
    description: 'Source of the reservation',
  })
  @IsOptional()
  @IsEnum(ReservationSource)
  reservationSource?: ReservationSource;

  @ApiProperty({
    description: 'Daily rate for the reservation',
    example: '150.00',
  })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  dailyRate: string;

  @ApiPropertyOptional({
    enum: DiscountType,
    description: 'Discount type',
  })
  @IsOptional()
  @IsEnum(DiscountType)
  discountType?: DiscountType;

  @ApiPropertyOptional({
    description: 'Discount value',
    example: '10.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  discountValue?: string;

  @ApiPropertyOptional({
    description: 'Discount reason',
  })
  @IsOptional()
  @IsString()
  discountReason?: string;

  @ApiPropertyOptional({
    enum: PaymentStatus,
    description: 'Payment status',
    default: PaymentStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @ApiPropertyOptional({
    description: 'Deposit required',
    example: '50.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  depositRequired?: string;

  @ApiPropertyOptional({
    enum: TaxType,
    description: 'Tax type',
    default: TaxType.INCLUSIVE,
  })
  @IsOptional()
  @IsEnum(TaxType)
  taxType?: TaxType;

  @ApiPropertyOptional({
    description: 'Reservation notes',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    enum: VehiclePickupLocationType,
    description: 'Pick up location type',
    default: VehiclePickupLocationType.OFFICE,
  })
  @IsOptional()
  @IsEnum(VehiclePickupLocationType)
  pickUpLocationType?: VehiclePickupLocationType;

  @ApiPropertyOptional({
    enum: VehiclePickupLocationType,
    description: 'Return location type',
    default: VehiclePickupLocationType.OFFICE,
  })
  @IsOptional()
  @IsEnum(VehiclePickupLocationType)
  returnLocationType?: VehiclePickupLocationType;

  @ApiPropertyOptional({
    description: 'Pick up location name',
  })
  @IsOptional()
  @IsString()
  pickUpLocation?: string;

  @ApiPropertyOptional({
    description: 'Return location name',
  })
  @IsOptional()
  @IsString()
  returnLocation?: string;

  @ApiPropertyOptional({
    description: 'Pick up address for ADDRESS type',
  })
  @IsOptional()
  @IsString()
  pickUpAddress?: string;

  @ApiPropertyOptional({
    description: 'Return address for ADDRESS type',
  })
  @IsOptional()
  @IsString()
  returnAddress?: string;

  @ApiPropertyOptional({
    description: 'Pick up instructions',
  })
  @IsOptional()
  @IsString()
  pickUpInstructions?: string;

  @ApiPropertyOptional({
    description: 'Return instructions',
  })
  @IsOptional()
  @IsString()
  returnInstructions?: string;

  @ApiProperty({
    description: 'Array of vehicles for this reservation',
    type: [CreateVehicleReservationVehicleDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateVehicleReservationVehicleDto)
  vehicles: CreateVehicleReservationVehicleDto[];
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  VehicleReservationStatus,
  VehiclePickupLocationType,
  PaymentStatus,
  TaxType,
  DiscountType,
} from '../../shared/types/vehicle.enum';
import {
  ReservationSource,
} from '../../shared/types/accommodation.enum';

export class VehicleReservationVehicleDto {
  @ApiProperty({ description: 'Vehicle reservation vehicle ID' })
  id: string;

  @ApiProperty({ description: 'Vehicle ID' })
  vehicleId: string;

  @ApiProperty({ description: 'Vehicle information' })
  vehicle: {
    id: string;
    vehicleNumber: string;
    make: string;
    model: string;
    year: number;
    color: string;
  };

  @ApiProperty({ description: 'Daily rate for this vehicle' })
  vehicleDailyRate: string;

  @ApiProperty({ description: 'Subtotal for this vehicle' })
  subtotal: string;

  @ApiProperty({ description: 'Total for this vehicle' })
  total: string;

  @ApiPropertyOptional({ enum: DiscountType, description: 'Vehicle-specific discount type' })
  vehicleDiscountType?: DiscountType;

  @ApiPropertyOptional({ description: 'Vehicle-specific discount value' })
  vehicleDiscountValue?: string;

  @ApiPropertyOptional({ description: 'Vehicle-specific discount amount' })
  vehicleDiscountAmount?: string;

  @ApiPropertyOptional({ description: 'Vehicle-specific discount reason' })
  vehicleDiscountReason?: string;

  @ApiPropertyOptional({ description: 'Whether vehicle comes with driver' })
  vehicleWithDriver?: boolean;

  @ApiPropertyOptional({ description: 'Driver staff member ID' })
  vehicleDriverId?: string;

  @ApiPropertyOptional({ description: 'Driver information' })
  vehicleDriver?: {
    id: string;
    displayName: string;
    profileImageUrl?: string;
  };

  @ApiPropertyOptional({ description: 'Driver notes for this vehicle' })
  vehicleDriverNotes?: string;

  @ApiPropertyOptional({ description: 'External driver name' })
  vehicleExternalDriverName?: string;

  @ApiPropertyOptional({ description: 'External driver license number' })
  vehicleExternalDriverLicense?: string;

  @ApiPropertyOptional({ description: 'External driver contact information' })
  vehicleExternalDriverContact?: string;

  @ApiPropertyOptional({ description: 'Vehicle-specific pickup time' })
  vehiclePickUpTime?: string;

  @ApiPropertyOptional({ description: 'Vehicle-specific return time' })
  vehicleReturnTime?: string;

  @ApiPropertyOptional({ description: 'Pick up odometer reading' })
  pickUpOdometer?: number;

  @ApiPropertyOptional({ description: 'Return odometer reading' })
  returnOdometer?: number;

  @ApiPropertyOptional({ description: 'Pick up fuel level (0-100%)' })
  pickUpFuelLevel?: number;

  @ApiPropertyOptional({ description: 'Return fuel level (0-100%)' })
  returnFuelLevel?: number;

  @ApiPropertyOptional({ description: 'Pick up condition notes' })
  pickUpConditionNotes?: string;

  @ApiPropertyOptional({ description: 'Return condition notes' })
  returnConditionNotes?: string;

  @ApiPropertyOptional({ description: 'Vehicle-specific notes' })
  notes?: string;

  @ApiProperty({ description: 'Vehicle order in multi-vehicle reservation' })
  vehicleOrder: number;

  @ApiPropertyOptional({ description: 'Tax rate ID for this vehicle' })
  vehicleTaxRateId?: string;

  @ApiPropertyOptional({ description: 'Tax rate information' })
  vehicleTaxRate?: {
    id: string;
    name: string;
    rate: string;
  };

  @ApiProperty({ description: 'Created at timestamp' })
  createdAt: string;

  @ApiProperty({ description: 'Updated at timestamp' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Created by user information' })
  createdBy?: {
    id: string;
    name: string;
    avatar?: string;
  };

  @ApiPropertyOptional({ description: 'Updated by user information' })
  updatedBy?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

export class VehicleReservationDto {
  @ApiProperty({ description: 'Vehicle reservation ID' })
  id: string;

  @ApiProperty({ description: 'Business ID' })
  businessId: string;

  @ApiProperty({ description: 'Reservation number' })
  reservationNumber: string;

  @ApiPropertyOptional({ description: 'External booking reference number' })
  referenceNumber?: string;

  @ApiPropertyOptional({ description: 'Customer ID' })
  customerId?: string;

  @ApiPropertyOptional({ description: 'Customer information' })
  customer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };

  @ApiProperty({ description: 'Pick up date and time' })
  pickUpDate: string;

  @ApiProperty({ description: 'Return date and time' })
  returnDate: string;

  @ApiPropertyOptional({ description: 'Actual pick up time' })
  actualPickUpTime?: string;

  @ApiPropertyOptional({ description: 'Actual return time' })
  actualReturnTime?: string;

  @ApiProperty({ enum: VehicleReservationStatus, description: 'Reservation status' })
  status: VehicleReservationStatus;

  @ApiPropertyOptional({ enum: ReservationSource, description: 'Source of the reservation' })
  reservationSource?: ReservationSource;

  @ApiProperty({ description: 'Daily rate for the reservation' })
  dailyRate: string;

  @ApiProperty({ description: 'Subtotal amount' })
  subtotal: string;

  @ApiProperty({ description: 'Total amount' })
  total: string;

  @ApiPropertyOptional({ enum: DiscountType, description: 'Discount type' })
  discountType?: DiscountType;

  @ApiPropertyOptional({ description: 'Discount value' })
  discountValue?: string;

  @ApiPropertyOptional({ description: 'Discount amount' })
  discountAmount?: string;

  @ApiPropertyOptional({ description: 'Discount reason' })
  discountReason?: string;

  @ApiProperty({ enum: PaymentStatus, description: 'Payment status' })
  paymentStatus: PaymentStatus;

  @ApiPropertyOptional({ description: 'Deposit required' })
  depositRequired?: string;

  @ApiPropertyOptional({ description: 'Deposit paid' })
  depositPaid?: string;

  @ApiPropertyOptional({ description: 'Balance due' })
  balanceDue?: string;

  @ApiProperty({ enum: TaxType, description: 'Tax type' })
  taxType: TaxType;

  @ApiPropertyOptional({ description: 'Reservation notes' })
  notes?: string;

  @ApiPropertyOptional({ description: 'Cancellation reason' })
  cancellationReason?: string;

  @ApiPropertyOptional({ description: 'Cancellation date' })
  cancellationDate?: string;

  @ApiProperty({ enum: VehiclePickupLocationType, description: 'Pick up location type' })
  pickUpLocationType: VehiclePickupLocationType;

  @ApiProperty({ enum: VehiclePickupLocationType, description: 'Return location type' })
  returnLocationType: VehiclePickupLocationType;

  @ApiPropertyOptional({ description: 'Pick up location name' })
  pickUpLocation?: string;

  @ApiPropertyOptional({ description: 'Return location name' })
  returnLocation?: string;

  @ApiPropertyOptional({ description: 'Pick up address for ADDRESS type' })
  pickUpAddress?: string;

  @ApiPropertyOptional({ description: 'Return address for ADDRESS type' })
  returnAddress?: string;

  @ApiPropertyOptional({ description: 'Pick up instructions' })
  pickUpInstructions?: string;

  @ApiPropertyOptional({ description: 'Return instructions' })
  returnInstructions?: string;

  @ApiPropertyOptional({ description: 'Flight arrival date for AIRPORT pickup' })
  flightArrivalDate?: string;

  @ApiPropertyOptional({ description: 'Flight arrival time (HH:MM format)' })
  flightArrivalTime?: string;

  @ApiPropertyOptional({ description: 'Flight departure date for AIRPORT return' })
  flightDepartureDate?: string;

  @ApiPropertyOptional({ description: 'Flight departure time (HH:MM format)' })
  flightDepartureTime?: string;

  @ApiPropertyOptional({ description: 'Arrival flight number' })
  arrivalFlightNumber?: string;

  @ApiPropertyOptional({ description: 'Departure flight number' })
  departureFlightNumber?: string;

  @ApiPropertyOptional({ description: 'Arrival airline' })
  arrivalAirline?: string;

  @ApiPropertyOptional({ description: 'Departure airline' })
  departureAirline?: string;

  @ApiProperty({ description: 'Whether confirmation was sent' })
  confirmationSent: boolean;

  @ApiPropertyOptional({ description: 'Confirmation sent at timestamp' })
  confirmationSentAt?: string;

  @ApiProperty({ description: 'Whether reminder was sent' })
  reminderSent: boolean;

  @ApiPropertyOptional({ description: 'Reminder sent at timestamp' })
  reminderSentAt?: string;

  @ApiProperty({ description: 'Array of vehicles for this reservation', type: [VehicleReservationVehicleDto] })
  vehicles: VehicleReservationVehicleDto[];

  @ApiProperty({ description: 'Created at timestamp' })
  createdAt: string;

  @ApiProperty({ description: 'Updated at timestamp' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Created by user information' })
  createdBy?: {
    id: string;
    name: string;
    avatar?: string;
  };

  @ApiPropertyOptional({ description: 'Updated by user information' })
  updatedBy?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  VehicleReservationDto,
  VehicleReservationListDto,
  VehicleReservationSlimDto,
  CreateVehicleReservationDto,
  UpdateVehicleReservationDto,
  BulkCreateVehicleReservationDto,
  VehicleReservationStatus,
  BulkUpdateVehicleReservationStatusDto,
  VehicleReservationTableData,
} from "@/types/vehicle-reservation";
import { ApiStatus } from "@/types/common";
import {
  GetVehicleReservationsSchema,
  CreateVehicleReservationSchema,
  UpdateVehicleReservationSchema,
  BulkCreateVehicleReservationsSchema,
  CheckVehicleReservationNumberSchema,
} from "./validations";

// Query functions
import {
  getVehicleReservationsTableData,
  getVehicleReservationsSlim,
  getVehicleReservation,
  checkVehicleReservationNumberAvailability,
  createVehicleReservation,
  bulkCreateVehicleReservations,
  updateVehicleReservation,
  deleteVehicleReservation,
  bulkDeleteVehicleReservations,
  updateVehicleReservationStatus,
  bulkUpdateVehicleReservationStatus,
} from "./queries";

// Query keys following categories module patterns
export const vehicleReservationKeys = {
  all: ["vehicle-reservations"] as const,
  lists: () => [...vehicleReservationKeys.all, "list"] as const,
  list: (params: GetVehicleReservationsSchema & { isDemo?: boolean }) =>
    [...vehicleReservationKeys.lists(), params] as const,
  details: () => [...vehicleReservationKeys.all, "detail"] as const,
  detail: (id: string) => [...vehicleReservationKeys.details(), id] as const,
  slim: () => [...vehicleReservationKeys.all, "slim"] as const,
  availability: (reservationNumber: string) =>
    [...vehicleReservationKeys.all, "availability", reservationNumber] as const,
};

// Hook to get vehicle reservations with pagination and filtering
export function useVehicleReservations(
  params: GetVehicleReservationsSchema & { isDemo?: boolean }
) {
  return useQuery({
    queryKey: vehicleReservationKeys.list(params),
    queryFn: () =>
      getVehicleReservationsTableData(params, params.isDemo || false),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get vehicle reservations in slim format
export function useVehicleReservationsSlim(isDemo: boolean = false) {
  return useQuery({
    queryKey: vehicleReservationKeys.slim(),
    queryFn: () => getVehicleReservationsSlim(isDemo),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to get a single vehicle reservation
export function useVehicleReservation(id: string, isDemo: boolean = false) {
  return useQuery({
    queryKey: vehicleReservationKeys.detail(id),
    queryFn: () => getVehicleReservation(id, isDemo),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to check vehicle reservation number availability
export function useVehicleReservationNumberAvailability(
  reservationNumber: string,
  isDemo: boolean = false
) {
  return useQuery({
    queryKey: vehicleReservationKeys.availability(reservationNumber),
    queryFn: () =>
      checkVehicleReservationNumberAvailability(reservationNumber, isDemo),
    enabled: !!reservationNumber && reservationNumber.length > 0,
    staleTime: 0, // Always fresh for availability checks
  });
}

// Hook to create a vehicle reservation
export function useCreateVehicleReservation(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateVehicleReservationDto) =>
      createVehicleReservation(data, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          response.message || "Vehicle reservation created successfully"
        );
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.lists(),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.slim(),
        });
      } else {
        toast.error(response.message || "Failed to create vehicle reservation");
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create vehicle reservation");
    },
  });
}

// Hook to bulk create vehicle reservations
export function useBulkCreateVehicleReservations(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (reservations: BulkCreateVehicleReservationDto) =>
      bulkCreateVehicleReservations(reservations, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          response.message || "Vehicle reservations created successfully"
        );
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.lists(),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.slim(),
        });
      } else {
        toast.error(
          response.message || "Failed to create vehicle reservations"
        );
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create vehicle reservations");
    },
  });
}

// Hook to update a vehicle reservation
export function useUpdateVehicleReservation(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdateVehicleReservationDto;
    }) => updateVehicleReservation(id, data, isDemo),
    onSuccess: (response, { id }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          response.message || "Vehicle reservation updated successfully"
        );
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.lists(),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.detail(id),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.slim(),
        });
      } else {
        toast.error(response.message || "Failed to update vehicle reservation");
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to update vehicle reservation");
    },
  });
}

// Hook to delete a vehicle reservation
export function useDeleteVehicleReservation(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteVehicleReservation(id, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          response.message || "Vehicle reservation deleted successfully"
        );
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.lists(),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.slim(),
        });
      } else {
        toast.error(response.message || "Failed to delete vehicle reservation");
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete vehicle reservation");
    },
  });
}

// Hook to bulk delete vehicle reservations
export function useBulkDeleteVehicleReservations(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (vehicleReservationIds: string[]) =>
      bulkDeleteVehicleReservations({ vehicleReservationIds }, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          response.message || "Vehicle reservations deleted successfully"
        );
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.lists(),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.slim(),
        });
      } else {
        toast.error(
          response.message || "Failed to delete vehicle reservations"
        );
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete vehicle reservations");
    },
  });
}

// Hook to update vehicle reservation status
export function useUpdateVehicleReservationStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      status,
    }: {
      id: string;
      status: VehicleReservationStatus;
    }) => updateVehicleReservationStatus(id, status, isDemo),
    onSuccess: (response, { id }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          response.message || "Vehicle reservation status updated successfully"
        );
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.lists(),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.detail(id),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.slim(),
        });
      } else {
        toast.error(
          response.message || "Failed to update vehicle reservation status"
        );
      }
    },
    onError: (error: any) => {
      toast.error(
        error.message || "Failed to update vehicle reservation status"
      );
    },
  });
}

// Hook to bulk update vehicle reservation status
export function useBulkUpdateVehicleReservationStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkUpdateVehicleReservationStatusDto) =>
      bulkUpdateVehicleReservationStatus(data, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          response.message ||
            "Vehicle reservation statuses updated successfully"
        );
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.lists(),
        });
        queryClient.invalidateQueries({
          queryKey: vehicleReservationKeys.slim(),
        });
      } else {
        toast.error(
          response.message || "Failed to update vehicle reservation statuses"
        );
      }
    },
    onError: (error: any) => {
      toast.error(
        error.message || "Failed to update vehicle reservation statuses"
      );
    },
  });
}

/**
 * Hook to get reservations by vehicle class
 */
export function useReservationsByVehicleClass(
  vehicleClass: VehicleClass,
  isDemo: boolean = false
) {
  return useQuery({
    queryKey: reservationKeys.byVehicleClass(vehicleClass),
    queryFn: () => getFilteredReservationsByVehicleClass(vehicleClass, isDemo),
    enabled: !!vehicleClass, // Only run the query if vehicleClass is provided
  });
}

/**
 * Hook to get a reservation by ID
 */
export function useReservationById(
  reservationId: string | undefined,
  isDemo: boolean = false
) {
  return useQuery<ApiResponse<VehicleReservationDetails | null>>({
    queryKey: reservationKeys.detail(reservationId || ""),
    queryFn: () => getReservationById(reservationId!, isDemo),
    enabled: !!reservationId, // Only run the query if reservationId is provided
  });
}

/**
 * Hook to get reservations by customer ID
 */
export function useReservationsByCustomerId(
  customerId: string | undefined,
  params: VehicleReservationFilterParams = {},
  isDemo: boolean = false
) {
  return useQuery({
    queryKey: [...reservationKeys.byCustomer(customerId || ""), params],
    queryFn: () => getReservationsByCustomerId(customerId!, params, isDemo),
    enabled: !!customerId, // Only run the query if customerId is provided
  });
}

/**
 * Hook to get reservations by status
 */
export function useReservationsByStatus(
  status: ReservationStatus | undefined,
  params: VehicleReservationFilterParams = {},
  isDemo: boolean = false
) {
  return useQuery({
    queryKey: status ? [...reservationKeys.byStatus(status), params] : [],
    queryFn: () => getReservationsByStatus(status!, params, isDemo),
    enabled: !!status, // Only run the query if status is provided
  });
}

/**
 * Hook to fetch reservation payments with pagination
 */
export function useReservationPayments(
  reservationId: string | undefined,
  page: number = 1,
  perPage: number = 10,
  isDemo: boolean = true
) {
  return useQuery({
    queryKey: [
      ...reservationKeys.payments(reservationId || ""),
      { page, perPage },
    ],
    queryFn: async () => {
      if (!reservationId) {
        return {
          data: [] as ReservationPayment[],
          totalItems: 0,
          pageCount: 0,
          currentPage: page,
        };
      }

      const response = isDemo
        ? await getDemoReservationPayments(reservationId, page, perPage)
        : await getReservationPayments(reservationId, page, perPage);

      if (response.status === ApiStatus.SUCCESS && response.data) {
        return response.data;
      }
      return {
        data: [] as ReservationPayment[],
        totalItems: 0,
        pageCount: 0,
        currentPage: page,
      };
    },
    enabled: !!reservationId,
  });
}

/**
 * Hook to update reservation status
 */
export function useUpdateReservationStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      status,
    }: {
      id: string;
      status: ReservationStatus;
    }) => {
      return isDemo
        ? await updateDemoReservationStatus(id, status)
        : await updateReservationStatus(id, status);
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({
        queryKey: reservationKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({ queryKey: reservationKeys.list() });
    },
  });
}

/**
 * Hook to record a payment for a reservation
 */
export function useRecordReservationPayment(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      reservationId,
      payment,
    }: {
      reservationId: string;
      payment: Omit<ReservationPayment, "id">;
    }) => {
      return await recordReservationPayment(reservationId, payment);
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({
        queryKey: reservationKeys.payments(variables.reservationId),
      });
      queryClient.invalidateQueries({
        queryKey: reservationKeys.detail(variables.reservationId),
      });
      queryClient.invalidateQueries({ queryKey: reservationKeys.list() });
    },
  });
}

/**
 * Hook to get available vehicles for a date range
 */
export function useAvailableVehicles(
  pickupDate: Date | undefined,
  returnDate: Date | undefined,
  vehicleClass?: VehicleClass,
  isDemo: boolean = false
) {
  return useQuery({
    queryKey: [
      "available-vehicles",
      pickupDate?.toISOString(),
      returnDate?.toISOString(),
      vehicleClass,
    ],
    queryFn: async () => {
      if (!pickupDate || !returnDate) {
        return [];
      }

      const response = isDemo
        ? await getDemoAvailableVehicles(pickupDate, returnDate, vehicleClass)
        : await getAvailableVehicles(pickupDate, returnDate, vehicleClass);

      if (response.status === ApiStatus.SUCCESS && response.data) {
        return response.data;
      }
      return [];
    },
    enabled: !!pickupDate && !!returnDate,
  });
}
